# Teste da Correção dos Tooltips - Hover em Texto

## O que foi corrigido:

O problema era que o código anterior só detectava quando o mouse estava diretamente sobre o elemento `<label>`, mas quando você passava o mouse sobre o texto dentro do label (que geralmente é um elemento `<span>` ou texto direto), o sistema não reconhecia.

## Mudanças implementadas:

1. **Função `isImageActionLabel()` melhorada**: Agora verifica tanto o elemento atual quanto seus elementos pais
2. **Nova função `checkLabelContent()`**: Função auxiliar para verificar o conteúdo do label
3. **Nova função `getActualLabel()`**: Retorna sempre o elemento label correto, mesmo quando hovering sobre filhos
4. **Event listener atualizado**: Usa a função `getActualLabel()` para garantir que sempre pegue o texto do label correto

## Como testar:

1. **Reinicie o Foocus** para carregar o script atualizado
2. **Vá para Input Image > Upscale or Variation**
3. **Teste os seguintes cenários**:
   - ✅ Passe o mouse sobre a **borda/fundo** dos botões (Disabled, Vary, etc.) - deve mostrar tooltip
   - ✅ Passe o mouse sobre o **texto** dos botões - agora também deve mostrar tooltip
   - ✅ Passe o mouse sobre qualquer **parte** do botão - deve sempre mostrar tooltip

## Estrutura HTML típica:

```html
<label class="svelte-xyz">
    <input type="radio" ...>
    <span>Vary (Subtle)</span>  <!-- Agora detecta hover aqui também -->
</label>
```

## O que mudou tecnicamente:

- **Antes**: `e.target` tinha que ser exatamente o `<label>`
- **Agora**: `e.target` pode ser o `<label>` OU qualquer elemento filho (como `<span>`, texto, etc.)
- **Resultado**: Tooltip aparece independente de onde você passa o mouse dentro do botão

## Verificação:

Se ainda não funcionar, abra o Developer Tools (F12) e verifique:
1. Se aparecem mensagens de "Image tooltips initialized"
2. Se há erros no console
3. Se os elementos têm a classe "svelte" corretamente

---

**Esta correção resolve o problema específico de hover em texto dentro dos botões de ação de imagem.**
