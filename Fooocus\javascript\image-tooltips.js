// Image Button Tooltips for Foocus - Final Working Version
// Targets LABEL elements with image action text

(function() {
    'use strict';
    
    let tooltip = null;
    let isInitialized = false;
    
    // Detailed tooltip messages for each image action
    const tooltipMessages = {
        'disable': 'Disable - Exclude this image from the generation process completely',
        'vary (subtle)': 'Vary (Subtle) - Generate similar images with minor creative differences while preserving the original composition and style',
        'vary (strong)': 'Vary (Strong) - Create bold variations with significant changes to composition, colors, and details while maintaining the core subject',
        'upscale (fast 2x)': 'Upscale (Fast 2x) - Quickly double the image resolution using efficient AI upscaling for faster results',
        'upscale (custom)': 'Upscale (Custom) - Advanced upscaling with customizable settings for optimal quality and specific requirements',
        'upscale': 'Upscale - Intelligently increase image resolution and enhance fine details using advanced AI algorithms',
        'vary': 'Vary - Generate creative variations of this image with different artistic interpretations'
    };
    
    // Create tooltip element
    function createTooltip() {
        if (tooltip) return tooltip;
        
        tooltip = document.createElement('div');
        tooltip.style.cssText = `
            position: fixed;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px 14px;
            border-radius: 8px;
            font-size: 13px;
            max-width: 320px;
            white-space: normal;
            word-wrap: break-word;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.25s ease;
            transform: translate(-50%, -100%);
            margin-top: -12px;
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
            font-family: system-ui, -apple-system, 'Segoe UI', sans-serif;
            line-height: 1.4;
            text-align: left;
        `;
        document.body.appendChild(tooltip);
        return tooltip;
    }
    
    // Get tooltip text for element
    function getTooltipText(element) {
        const text = element.textContent.trim().toLowerCase();
        
        // Check for exact matches first
        for (const [key, message] of Object.entries(tooltipMessages)) {
            if (text === key) {
                return message;
            }
        }
        
        // Check for partial matches
        if (text.includes('vary') && text.includes('strong')) {
            return tooltipMessages['vary (strong)'];
        }
        if (text.includes('vary') && text.includes('subtle')) {
            return tooltipMessages['vary (subtle)'];
        }
        if (text.includes('upscale') && text.includes('fast')) {
            return tooltipMessages['upscale (fast 2x)'];
        }
        if (text.includes('upscale') && text.includes('custom')) {
            return tooltipMessages['upscale (custom)'];
        }
        if (text.includes('upscale')) {
            return tooltipMessages['upscale'];
        }
        if (text.includes('vary')) {
            return tooltipMessages['vary'];
        }
        if (text.includes('disable')) {
            return tooltipMessages['disable'];
        }
        
        // Generic fallback
        return null;
    }
    
    // Show tooltip
    function showTooltip(e, text) {
        const tooltip = createTooltip();
        tooltip.textContent = text;
        tooltip.style.left = e.clientX + 'px';
        tooltip.style.top = e.clientY + 'px';
        tooltip.style.opacity = '1';
        
        // Adjust position if tooltip would go off screen
        setTimeout(() => {
            const rect = tooltip.getBoundingClientRect();
            
            // Adjust horizontal position
            if (rect.right > window.innerWidth - 15) {
                tooltip.style.left = (window.innerWidth - rect.width - 15) + 'px';
            }
            if (rect.left < 15) {
                tooltip.style.left = '15px';
            }
            
            // Adjust vertical position
            if (rect.top < 15) {
                tooltip.style.transform = 'translate(-50%, 15px)';
                tooltip.style.marginTop = '0px';
            }
        }, 0);
    }
    
    // Hide tooltip
    function hideTooltip() {
        if (tooltip) {
            tooltip.style.opacity = '0';
        }
    }
    
    // Check if element is an image action label or child of one
    function isImageActionLabel(element) {
        if (!element) return false;

        // Check if the element itself is a label
        if (element.tagName === 'LABEL') {
            return checkLabelContent(element);
        }

        // Check if the element is inside a label (for text elements, spans, etc.)
        const parentLabel = element.closest('label');
        if (parentLabel) {
            return checkLabelContent(parentLabel);
        }

        return false;
    }

    // Helper function to check if a label has image action content
    function checkLabelContent(labelElement) {
        const text = labelElement.textContent.trim().toLowerCase();
        const classList = labelElement.className || '';

        // Must have svelte class (indicates it's part of the UI framework)
        const hasSvelteClass = classList.includes('svelte');

        // Must have image action text
        const hasImageActionText = (
            text.includes('disable') ||
            text.includes('vary') ||
            text.includes('upscale')
        );

        return hasSvelteClass && hasImageActionText;
    }

    // Get the actual label element (whether we're hovering the label or its child)
    function getActualLabel(element) {
        if (element.tagName === 'LABEL') {
            return element;
        }
        return element.closest('label');
    }
    
    // Initialize event listeners
    function init() {
        if (isInitialized) return;
        isInitialized = true;
        
        console.log('Image tooltips initialized for LABEL elements');
        
        // Event delegation for mouseover
        document.addEventListener('mouseover', function(e) {
            const element = e.target;

            if (isImageActionLabel(element)) {
                // Get the actual label element (in case we're hovering a child)
                const actualLabel = getActualLabel(element);
                const tooltipText = getTooltipText(actualLabel);
                if (tooltipText) {
                    showTooltip(e, tooltipText);
                }
            }
        });

        // Hide tooltip on mouseout
        document.addEventListener('mouseout', function(e) {
            if (isImageActionLabel(e.target)) {
                hideTooltip();
            }
        });
        
        // Update tooltip position on mouse move
        document.addEventListener('mousemove', function(e) {
            if (tooltip && tooltip.style.opacity === '1') {
                tooltip.style.left = e.clientX + 'px';
                tooltip.style.top = e.clientY + 'px';
                
                // Keep adjusting position to avoid screen edges
                const rect = tooltip.getBoundingClientRect();
                
                // Horizontal adjustments
                if (rect.right > window.innerWidth - 15) {
                    tooltip.style.left = (window.innerWidth - rect.width - 15) + 'px';
                }
                if (rect.left < 15) {
                    tooltip.style.left = '15px';
                }
                
                // Vertical adjustments
                if (rect.top < 15) {
                    tooltip.style.transform = 'translate(-50%, 15px)';
                    tooltip.style.marginTop = '0px';
                } else {
                    tooltip.style.transform = 'translate(-50%, -100%)';
                    tooltip.style.marginTop = '-12px';
                }
            }
        });
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Multiple fallback initializations for dynamic content
    setTimeout(init, 1000);
    setTimeout(init, 3000);
    setTimeout(init, 5000);
    
    // Initialize on UI updates if available
    if (typeof onAfterUiUpdate !== 'undefined') {
        onAfterUiUpdate(init);
    }
    
})();