# Foocus - Documentação Completa

## 📋 Índice
1. [<PERSON><PERSON><PERSON> Geral](#visão-geral)
2. [Arquitetura do Sistema](#arquitetura-do-sistema)
3. [Dependências](#dependências)
4. [Funcionalidades](#funcionalidades)
5. [Modelos e Estilos](#modelos-e-estilos)
6. [Pipeline de Geração](#pipeline-de-geração)
7. [Configuração e Presets](#configuração-e-presets)
8. [Instalação e Execução](#instalação-e-execução)

---

## 🎯 Visão Geral

**Foocus** é um software avançado de geração de imagens baseado em IA que utiliza modelos Stable Diffusion XL (SDXL). Desenvolvido com foco na simplicidade de uso e qualidade de resultados, o Foocus oferece uma interface web intuitiva construída com Gradio.

### Características Principais:
- **Interface Web Moderna**: Interface baseada em Gradio com design responsivo
- **Modelos SDXL**: Suporte completo para modelos Stable Diffusion XL
- **Processamento Assíncrono**: Sistema de workers assíncronos para geração eficiente
- **Múltiplas Funcionalidades**: Text-to-Image, Inpainting, Outpainting, Upscaling, ControlNet
- **Presets Especializados**: Configurações otimizadas para diferentes estilos (Anime, Realista, etc.)

---

## 🏗️ Arquitetura do Sistema

### Estrutura de Arquivos Principal:
```
Fooocus/
├── launch.py              # Ponto de entrada principal
├── webui.py               # Interface web Gradio
├── modules/
│   ├── core.py            # Funcionalidades centrais de IA
│   ├── async_worker.py    # Sistema de workers assíncronos
│   ├── default_pipeline.py # Pipeline de processamento
│   ├── config.py          # Configurações do sistema
│   └── flags.py           # Constantes e flags
├── ldm_patched/           # Módulos LDM modificados
├── presets/               # Configurações pré-definidas
├── models/                # Modelos de IA
└── outputs/               # Imagens geradas
```

### Fluxo de Inicialização:
1. **launch.py** → Prepara ambiente e dependências
2. **webui.py** → Inicializa interface Gradio
3. **async_worker.py** → Configura sistema de processamento
4. **default_pipeline.py** → Carrega modelos e pipeline

---

## 📦 Dependências

### Dependências Principais (requirements_versions.txt):
```
torch==2.1.0              # Framework de deep learning
torchvision==0.16.0       # Processamento de imagens
gradio==3.41.2            # Interface web
transformers==4.42.4      # Modelos de linguagem
safetensors==0.4.3        # Formato seguro de tensores
accelerate==0.32.1        # Aceleração de modelos
pillow==10.4.0            # Processamento de imagens
opencv-contrib-python-headless==*********  # Visão computacional
onnxruntime==1.18.1       # Runtime ONNX
rembg==2.0.57             # Remoção de background
segment_anything==1.0     # Segmentação SAM
groundingdino-py==0.4.0   # Detecção de objetos
```

### Dependências de Sistema:
- **Python 3.8+**
- **CUDA 12.1** (para GPU NVIDIA)
- **8GB+ VRAM** (recomendado)
- **16GB+ RAM** (recomendado)

---

## ⚡ Funcionalidades

### 1. **Text-to-Image**
- Geração de imagens a partir de prompts de texto
- Suporte a prompts positivos e negativos
- Múltiplos estilos e configurações

### 2. **Image Prompt (IP-Adapter)**
- Uso de imagens como referência
- FaceSwap para troca de rostos
- Controle de força e influência

### 3. **Inpaint/Outpaint**
- **Inpaint**: Modificação de áreas específicas
- **Outpaint**: Extensão de imagens (Up/Down/Left/Right)
- Algoritmos proprietários otimizados

### 4. **Upscale/Variation**
- **Upscale 1.5x/2x**: Aumento de resolução
- **Upscale Fast 2x**: Upscaling rápido
- **Vary Subtle/Strong**: Variações da imagem

### 5. **ControlNet**
- **PyraCanny**: Detecção de bordas
- **CPDS**: Controle de pose e profundidade
- Controle preciso de composição

### 6. **Enhance**
- Melhoria automática de qualidade
- Processamento em múltiplas etapas
- Otimização de detalhes

### 7. **Describe**
- Análise automática de imagens
- Geração de prompts descritivos
- Suporte para fotos e arte/anime

### 8. **Metadata**
- Importação de parâmetros de imagens
- Preservação de configurações
- Compatibilidade com outros softwares

---

## 🎨 Modelos e Estilos

### Modelos Base Suportados:
- **SDXL Base**: Modelo padrão Stable Diffusion XL
- **SDXL Refiner**: Modelo de refinamento
- **JuggernautXL**: Modelo otimizado (padrão)
- **AnimaPencilXL**: Especializado em anime
- **RealisticStockPhoto**: Focado em realismo
- **PlaygroundV2.5**: Modelo versátil

### Estilos Disponíveis:
- **Fooocus V2**: Estilo base otimizado
- **Fooocus Enhance**: Melhorias de qualidade
- **Fooocus Sharp**: Aumento de nitidez
- **Fooocus Cinematic**: Estilo cinematográfico
- **Fooocus Photograph**: Estilo fotográfico
- **Fooocus Masterpiece**: Para obras de arte
- **Fooocus Semi Realistic**: Realismo moderado

### Samplers Disponíveis:
- **DPM++ 2M SDE GPU**: Padrão (rápido e qualidade)
- **DPM++ 2M**: Versão CPU
- **Euler/Euler A**: Samplers clássicos
- **DDIM**: Determinístico
- **LCM**: Ultra-rápido
- **UniPC**: Eficiente

### Schedulers:
- **Karras**: Padrão (melhor qualidade)
- **Normal**: Scheduler linear
- **Exponential**: Decaimento exponencial
- **EDM Playground V2.5**: Para Playground models

---

## 🔄 Pipeline de Geração

### Fluxo Principal:
1. **Entrada do Usuário** → Interface Gradio recebe parâmetros
2. **Processamento de Prompt** → Análise e expansão do texto
3. **Carregamento de Modelos** → Base model + LoRAs + VAE
4. **Preparação de Latents** → Criação do espaço latente inicial
5. **Aplicação de ControlNets** → Se habilitados
6. **Processo de Difusão** → Sampling iterativo
7. **Refinamento** → Se refiner habilitado
8. **Decodificação VAE** → Conversão para imagem
9. **Pós-processamento** → Upscaling, enhance, etc.
10. **Salvamento** → Arquivo final com metadata

### Componentes do Pipeline:

#### async_worker.py:
- Gerencia filas de processamento
- Controla workers assíncronos
- Coordena etapas de geração

#### default_pipeline.py:
- Implementa pipeline de difusão
- Gerencia modelos carregados
- Controla processo de sampling

#### core.py:
- Operações fundamentais de IA
- Interfaces com modelos LDM
- Funções de encoding/decoding

### Processo de Sampling:
```python
# Pseudocódigo simplificado
latent = create_initial_latent(width, height, seed)
for step in range(steps):
    noise_pred = unet(latent, timestep, prompt_embedding)
    latent = scheduler.step(noise_pred, timestep, latent)
image = vae.decode(latent)
```

---

## ⚙️ Configuração e Presets

### Presets Disponíveis:

#### Default (default.json):
- Modelo: JuggernautXL v8
- CFG Scale: 4.0
- Sampler: DPM++ 2M SDE GPU
- Estilos: Fooocus V2, Enhance, Sharp

#### Anime (anime.json):
- Modelo: AnimaPencilXL v5
- CFG Scale: 6.0
- Aspect Ratio: 896×1152
- Estilos: Fooocus V2, Semi Realistic, Masterpiece

#### Realistic (realistic.json):
- Modelo: RealisticStockPhoto v20
- CFG Scale: 3.0
- LoRA: Film Photography Style
- Prompt Negativo: Otimizado para realismo

#### SAI (sai.json):
- Modelos oficiais Stability AI
- Base + Refiner
- Configurações conservadoras

### Parâmetros Configuráveis:
- **Performance**: Speed/Quality/Extreme Speed
- **Aspect Ratio**: Múltiplas proporções
- **Steps**: 1-150 (padrão: 30)
- **CFG Scale**: 1.0-30.0 (padrão: 4.0-7.0)
- **Seed**: Controle de aleatoriedade
- **LoRAs**: Até 5 LoRAs simultâneos

---

## 🚀 Instalação e Execução

### Métodos de Instalação:

#### 1. Instalação Padrão:
```bash
git clone https://github.com/lllyasviel/Fooocus.git
cd Fooocus
python launch.py
```

#### 2. Docker:
```bash
docker compose up
# ou
docker run -p 7865:7865 ghcr.io/lllyasviel/fooocus
```

#### 3. Presets Específicos:
```bash
python launch.py --preset anime
python launch.py --preset realistic
```

### Argumentos de Linha de Comando:
- `--listen`: Aceita conexões externas
- `--port 7865`: Define porta
- `--share`: Cria link público Gradio
- `--preset [nome]`: Carrega preset específico
- `--gpu-device-id [id]`: Seleciona GPU

### Primeira Execução:
1. Download automático de modelos (~4-6GB)
2. Instalação de dependências
3. Configuração inicial
4. Interface disponível em http://localhost:7865

---

## 📝 Notas Técnicas

### Otimizações:
- **Model Management**: Carregamento inteligente de modelos
- **Memory Optimization**: Gestão eficiente de VRAM
- **Async Processing**: Processamento não-bloqueante
- **Tiled VAE**: Para imagens de alta resolução

### Compatibilidade:
- **Windows**: Suporte completo
- **Linux**: Suporte completo
- **macOS**: Suporte com limitações (CPU/MPS)
- **Google Colab**: Notebooks disponíveis

### Formatos Suportados:
- **Entrada**: PNG, JPEG, WebP
- **Saída**: PNG, JPEG, WebP
- **Modelos**: SafeTensors, Checkpoint (.ckpt)

---

## 🔧 Detalhes Técnicos Avançados

### Sistema de LoRAs:
O Foocus suporte até 5 LoRAs simultâneos com pesos individuais:
- **Peso**: 0.0 a 2.0 (padrão: 1.0)
- **Aplicação**: Automática durante o sampling
- **Tipos**: Style LoRAs, Character LoRAs, Concept LoRAs

### Inpainting Engine:
- **v1**: Engine básico
- **v2.5**: Melhorado para detalhes
- **v2.6**: Versão mais recente com otimizações
- **Algoritmo Proprietário**: Superior ao SDXL padrão

### ControlNet Integrado:
- **PyraCanny**: Detecção de bordas multi-escala
- **CPDS**: Control Pose Depth Segmentation
- **ImagePrompt**: IP-Adapter otimizado
- **FaceSwap**: Troca de rostos com preservação de identidade

### Máscaras de Inpainting:
- **U2Net**: Segmentação geral
- **U2Net Human**: Específico para pessoas
- **U2Net Cloth**: Segmentação de roupas
- **SAM**: Segment Anything Model
- **ISNet**: Segmentação de alta qualidade

### Performance Modes:
- **Speed**: 30 steps, qualidade balanceada
- **Quality**: 60 steps, máxima qualidade
- **Extreme Speed**: 8 steps, para testes rápidos

### Aspect Ratios Suportados:
- **Square**: 1024×1024, 896×896
- **Portrait**: 896×1152, 832×1216
- **Landscape**: 1152×896, 1216×832
- **Widescreen**: 1344×768, 1536×640

---

## 📊 Monitoramento e Logs

### Sistema de Logs:
- **Progress Tracking**: Acompanhamento em tempo real
- **Error Handling**: Tratamento robusto de erros
- **Performance Metrics**: Métricas de velocidade e uso de memória
- **Image Metadata**: Preservação completa de parâmetros

### Debugging:
- **CN Preprocessor**: Visualização de ControlNet
- **Intermediate Results**: Resultados intermediários
- **Memory Usage**: Monitoramento de VRAM
- **Model Loading**: Status de carregamento

---

## 🌐 API e Integração

### Interface Gradio:
- **RESTful API**: Endpoints automáticos
- **WebSocket**: Comunicação em tempo real
- **Queue System**: Gerenciamento de filas
- **Authentication**: Sistema de autenticação opcional

### Extensibilidade:
- **Custom Styles**: Adição de estilos personalizados
- **Model Integration**: Suporte a novos modelos
- **Preprocessing**: Hooks para pré-processamento
- **Post-processing**: Filtros personalizados

---

## 🔒 Segurança e Privacidade

### NSFW Detection:
- **Automatic Censoring**: Detecção automática de conteúdo
- **Configurable**: Pode ser habilitado/desabilitado
- **Privacy**: Processamento local

### Data Privacy:
- **Local Processing**: Tudo processado localmente
- **No Telemetry**: Sem coleta de dados
- **Offline Capable**: Funciona sem internet após setup

---

## 🎯 Casos de Uso Comuns

### 1. Arte Digital:
- Concept art para jogos
- Ilustrações para livros
- Arte para redes sociais

### 2. Design Gráfico:
- Mockups e protótipos
- Texturas e padrões
- Elementos visuais

### 3. Fotografia:
- Retoque e melhoramento
- Extensão de backgrounds
- Remoção de objetos

### 4. Educação:
- Material didático visual
- Ilustrações científicas
- Recursos educacionais

---

## 🚨 Troubleshooting

### Problemas Comuns:

#### 1. Erro de VRAM:
```
Solução: Reduzir resolução ou usar --lowvram
```

#### 2. Modelos não carregam:
```
Verificar: Espaço em disco e conexão de internet
```

#### 3. Interface não abre:
```
Verificar: Porta 7865 disponível, firewall
```

#### 4. Geração lenta:
```
Otimizar: Performance mode, reduzir steps
```

### Logs Importantes:
- `launch.py`: Logs de inicialização
- `webui.py`: Logs da interface
- `async_worker.py`: Logs de processamento

---

## 📈 Roadmap e Futuro

### Recursos Planejados:
- **Video Generation**: Geração de vídeos
- **3D Integration**: Suporte a modelos 3D
- **Mobile Support**: Interface mobile
- **Cloud Integration**: Opções de nuvem

### Melhorias Contínuas:
- **Performance**: Otimizações constantes
- **Quality**: Novos modelos e técnicas
- **Usability**: Interface mais intuitiva
- **Compatibility**: Suporte a mais formatos

---

## 📚 Recursos Adicionais

### Documentação Oficial:
- **GitHub**: https://github.com/lllyasviel/Fooocus
- **Wiki**: Documentação detalhada
- **Issues**: Suporte da comunidade

### Comunidade:
- **Discord**: Comunidade ativa
- **Reddit**: Discussões e exemplos
- **YouTube**: Tutoriais e demonstrações

### Modelos Recomendados:
- **CivitAI**: Repositório de modelos
- **HuggingFace**: Modelos oficiais
- **Community**: Modelos da comunidade

---

*Documentação atualizada para Foocus v2.5.5 - Última atualização: 2024*
